# 流量镜像工具 .gitignore 配置
# 用于忽略不需要版本控制的文件

# =============================================================================
# 二进制文件和可执行文件
# =============================================================================

# 主程序二进制文件
traffic-mirror
traffic-mirror.exe

# 平台特定的二进制文件
traffic-mirror-*
*-amd64
*-arm64
*-linux-*
*-darwin-*
*-windows-*

# Go编译产物
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件
*.test

# 输出的二进制文件 (build)
/bin/
/build/
/dist/

# =============================================================================
# 日志文件
# =============================================================================

# 应用日志
*.log
*.log.*
logs/
log/

# 系统日志
*.out
*.err

# 日志轮转文件
*.log.gz
*.log.bz2
*.log.xz
*.log.1
*.log.2
*.log.3
*.log.4
*.log.5

# 特定日志文件
request.log*
error.log*
access.log*
debug.log*
traffic-mirror.log*

# =============================================================================
# 压缩包和归档文件
# =============================================================================

# 常见压缩格式
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz
*.tgz
*.zip
*.rar
*.7z
*.gz
*.bz2
*.xz

# 发行包
traffic-mirror-*.tar.gz
traffic-mirror-*.zip

# 备份文件
*.backup
*.bak
*.old
*.orig

# =============================================================================
# 构建和发布目录
# =============================================================================

# 构建目录
build/
dist/
packages/
releases/
release/
target/

# 临时构建文件
temp/
tmp/
.tmp/

# 发布产物
*.deb
*.rpm
*.pkg
*.dmg
*.msi

# =============================================================================
# Go语言特定文件
# =============================================================================

# 依赖管理
vendor/

# Go模块缓存
go.sum.backup

# 测试覆盖率文件
*.cover
*.coverprofile
coverage.out
coverage.html
coverage.txt

# Go工作区文件
go.work
go.work.sum

# 性能分析文件
*.prof
*.pprof
cpu.prof
mem.prof
block.prof
mutex.prof

# =============================================================================
# 开发工具和IDE文件
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# GoLand/IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Vim
*.swp
*.swo
*~
.*.swp
.*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# =============================================================================
# 操作系统文件
# =============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 配置和敏感文件
# =============================================================================

# 环境配置文件
.env
.env.local
.env.*.local
.environment

# 敏感配置文件（保留模板）
config_local.yaml
config_secret.yaml
config_private.yaml
*_secret.yaml
*_private.yaml

# 密钥和证书文件
*.key
*.pem
*.crt
*.cert
*.p12
*.pfx
secrets/
certs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# =============================================================================
# 运行时文件
# =============================================================================

# 进程ID文件
*.pid
*.lock

# 套接字文件
*.sock

# 临时文件
*.tmp
*.temp
.cache/
cache/

# 运行时数据
data/
runtime/

# =============================================================================
# 测试和调试文件
# =============================================================================

# 测试输出
test-results/
test-reports/
*.test.log

# 调试文件
debug
debug.log
*.debug

# 基准测试结果
*.bench
benchmark/

# =============================================================================
# 文档生成文件
# =============================================================================

# 生成的文档
docs/_build/
docs/build/
site/

# API文档
api-docs/
swagger-ui/

# =============================================================================
# 容器和虚拟化
# =============================================================================

# Docker
.dockerignore.bak
docker-compose.override.yml
docker-compose.local.yml

# Vagrant
.vagrant/

# =============================================================================
# 云平台和CI/CD
# =============================================================================

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# CI/CD
.github/workflows/local.yml
.gitlab-ci.local.yml

# =============================================================================
# 项目特定文件
# =============================================================================

# 本地开发配置
config_dev_local.yaml
config_test_local.yaml

# 本地脚本
local_*.sh
dev_*.sh
test_*.sh

# 开发者笔记
NOTES.md
TODO.md
SCRATCH.md

# 本地工具
tools/local/
scripts/local/

# 性能测试结果
perf/
benchmarks/
load-test/

# 监控数据
metrics/
monitoring/

# =============================================================================
# 备份和版本文件
# =============================================================================

# 自动备份
*.backup.*
*_backup_*
backup_*

# 版本文件（如果自动生成）
VERSION.auto
version.auto

# 补丁文件
*.patch
*.diff

# =============================================================================
# 其他
# =============================================================================

# 编辑器临时文件
.#*
\#*#

# 网络文件
*.url
*.webloc

# 压缩工具临时文件
*.zip.tmp
*.tar.tmp

# 系统缓存
.cache
.npm
.yarn

# 本地环境变量
.envrc