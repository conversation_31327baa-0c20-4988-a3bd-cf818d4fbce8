package handler

import (
	"fmt"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/forwarder"
	"traffic-mirror/kafka"
	"traffic-mirror/logger"
	"traffic-mirror/message"
)

// MessageHandler 消息处理器
type MessageHandler struct {
	config     *config.Config
	logManager *logger.LoggerManager
	parser     *message.Parser
	forwarder  *forwarder.HTTPForwarder
	metrics    *HandlerMetrics
}

// HandlerMetrics 处理器统计指标
type HandlerMetrics struct {
	TotalMessages     int64 `json:"total_messages"`
	ProcessedMessages int64 `json:"processed_messages"`
	SkippedMessages   int64 `json:"skipped_messages"`
	ErrorMessages     int64 `json:"error_messages"`
	StartTime         int64 `json:"start_time"`
	LastMessageTime   int64 `json:"last_message_time"`
}

// NewMessageHandler 创建新的消息处理器
func NewMessageHandler(cfg *config.Config, logManager *logger.LoggerManager) (*MessageHandler, error) {
	// 创建消息解析器 - 使用请求日志器
	requestLogger := logManager.GetLogger(logger.LogTypeRequest)
	parser := message.NewParser(requestLogger, cfg.Filter.MessageType)

	// 创建HTTP转发器 - 使用请求日志器
	httpForwarder := forwarder.NewHTTPForwarder(cfg, requestLogger)

	handler := &MessageHandler{
		config:     cfg,
		logManager: logManager,
		parser:     parser,
		forwarder:  httpForwarder,
		metrics: &HandlerMetrics{
			StartTime: time.Now().Unix(),
		},
	}

	return handler, nil
}

// createMessageLogFields 创建消息的通用日志字段
func (h *MessageHandler) createMessageLogFields(message *kafka.Message) logrus.Fields {
	return logrus.Fields{
		"topic":            message.Topic,
		"partition":        message.Partition,
		"offset":           message.Offset,
		"kafka_timestamp":  message.Time.Format("2006-01-02 15:04:05.000"),
		"server_timestamp": time.Now().Format("2006-01-02 15:04:05.000"),
		"message_id":       fmt.Sprintf("%s-%d-%d", message.Topic, message.Partition, message.Offset),
		"time_diff_ms":     time.Since(message.Time).Milliseconds(),
	}
}

// HandleMessage 实现kafka.MessageHandler接口
func (h *MessageHandler) HandleMessage(consumer *kafka.Consumer, message *kafka.Message) error {
	// 更新统计指标
	atomic.AddInt64(&h.metrics.TotalMessages, 1)
	atomic.StoreInt64(&h.metrics.LastMessageTime, time.Now().Unix())

	// 获取消息基本信息用于日志
	messageInfo := h.parser.GetMessageInfo(message.Value)

	// 时间过滤检查
	if h.config.Filter.TimeFilter.Enabled {
		if !h.isMessageWithinTimeWindow(message) {
			atomic.AddInt64(&h.metrics.SkippedMessages, 1)
			logFields := h.createMessageLogFields(message)
			logFields["message_info"] = messageInfo
			logFields["reason"] = "outside_time_window"
			logFields["processing_status"] = "skipped_time_filter"
			h.logManager.LogRequest(logrus.DebugLevel, logFields, "Message skipped due to time filter")
			return nil
		}
	}

	// 记录处理消息的调试日志
	logFields := h.createMessageLogFields(message)
	logFields["message_info"] = messageInfo
	h.logManager.LogRequest(logrus.DebugLevel, logFields, "Processing message")

	// 解析和处理消息
	request, err := h.parser.ProcessMessage(consumer, message.Value)
	if err != nil {
		atomic.AddInt64(&h.metrics.ErrorMessages, 1)
		// 解析失败只记录日志，不返回错误，继续处理下一条消息
		logFields := h.createMessageLogFields(message)
		logFields["message_info"] = messageInfo
		logFields["error"] = err.Error()
		logFields["processing_status"] = "parse_failed"
		h.logManager.LogError(logrus.WarnLevel, logFields, "Failed to process message, skipping")
		return nil
	}

	// 如果返回nil，表示消息被跳过（不是目标类型）
	if request == nil {
		atomic.AddInt64(&h.metrics.SkippedMessages, 1)
		logFields := h.createMessageLogFields(message)
		logFields["message_info"] = messageInfo
		logFields["processing_status"] = "skipped_not_target_type"
		h.logManager.LogRequest(logrus.DebugLevel, logFields, "Message skipped (not target type)")
		return nil
	}

	// 转发请求 (Fire-and-Forget模式，不检查错误)
	h.forwarder.ForwardRequest(request)

	atomic.AddInt64(&h.metrics.ProcessedMessages, 1)

	// 增强的日志记录，包含完整的消息元数据
	logFields = h.createMessageLogFields(message)
	logFields["trace_id"] = request.Header.TraceID
	logFields["app_id"] = request.Header.AppID
	logFields["prod_code"] = request.Header.ProdCode
	logFields["query"] = request.Payload.Query
	logFields["intent"] = request.Payload.Intent
	logFields["processing_status"] = "forwarded"
	h.logManager.LogRequest(logrus.InfoLevel, logFields, "Message processed and forwarded successfully")

	return nil
}

// isMessageWithinTimeWindow 检查消息是否在时间窗口内
func (h *MessageHandler) isMessageWithinTimeWindow(message *kafka.Message) bool {
	timeFilter := h.config.Filter.TimeFilter
	now := time.Now()

	var messageTime time.Time
	switch timeFilter.TimeMode {
	case "kafka_time":
		messageTime = message.Time
	case "server_time":
		// 使用服务器接收时间（当前时间）
		messageTime = now
	default:
		// 默认使用Kafka时间
		messageTime = message.Time
	}

	// 计算时间差（绝对值）
	timeDiff := now.Sub(messageTime)
	if timeDiff < 0 {
		timeDiff = -timeDiff
	}

	// 检查是否在时间窗口内（考虑容忍度）
	windowDuration := time.Duration(timeFilter.WindowSeconds) * time.Second
	toleranceDuration := time.Duration(timeFilter.ToleranceSeconds) * time.Second
	maxAllowedDiff := windowDuration + toleranceDuration

	isWithinWindow := timeDiff <= maxAllowedDiff

	// 记录时间过滤的详细信息（仅在调试模式下）
	h.logManager.LogRequest(logrus.DebugLevel, logrus.Fields{
		"kafka_time":        messageTime,
		"server_time":       now,
		"time_diff_seconds": timeDiff.Seconds(),
		"window_seconds":    timeFilter.WindowSeconds,
		"tolerance_seconds": timeFilter.ToleranceSeconds,
		"max_allowed_diff":  maxAllowedDiff.Seconds(),
		"is_within_window":  isWithinWindow,
		"time_mode":         timeFilter.TimeMode,
	}, "Time filter evaluation")

	return isWithinWindow
}

// GetMetrics 获取处理器统计指标
func (h *MessageHandler) GetMetrics() *HandlerMetrics {
	return &HandlerMetrics{
		TotalMessages:     atomic.LoadInt64(&h.metrics.TotalMessages),
		ProcessedMessages: atomic.LoadInt64(&h.metrics.ProcessedMessages),
		SkippedMessages:   atomic.LoadInt64(&h.metrics.SkippedMessages),
		ErrorMessages:     atomic.LoadInt64(&h.metrics.ErrorMessages),
		StartTime:         h.metrics.StartTime,
		LastMessageTime:   atomic.LoadInt64(&h.metrics.LastMessageTime),
	}
}

// GetForwarderMetrics 获取转发器统计指标
func (h *MessageHandler) GetForwarderMetrics() *forwarder.Metrics {
	return h.forwarder.GetMetrics()
}

// GetProcessingRate 获取处理速率（消息/秒）
func (h *MessageHandler) GetProcessingRate() float64 {
	metrics := h.GetMetrics()
	duration := time.Now().Unix() - metrics.StartTime
	if duration == 0 {
		return 0.0
	}
	return float64(metrics.TotalMessages) / float64(duration)
}

// GetSuccessRate 获取成功率
func (h *MessageHandler) GetSuccessRate() float64 {
	metrics := h.GetMetrics()
	if metrics.TotalMessages == 0 {
		return 0.0
	}
	return float64(metrics.ProcessedMessages) / float64(metrics.TotalMessages) * 100
}

// PrintStats 打印统计信息
func (h *MessageHandler) PrintStats() {
	handlerMetrics := h.GetMetrics()
	forwarderMetrics := h.GetForwarderMetrics()

	h.logManager.LogStats(logrus.InfoLevel, logrus.Fields{
		"handler_metrics": map[string]interface{}{
			"total_messages":     handlerMetrics.TotalMessages,
			"processed_messages": handlerMetrics.ProcessedMessages,
			"skipped_messages":   handlerMetrics.SkippedMessages,
			"error_messages":     handlerMetrics.ErrorMessages,
			"processing_rate":    fmt.Sprintf("%.2f msg/s", h.GetProcessingRate()),
			"success_rate":       fmt.Sprintf("%.2f%%", h.GetSuccessRate()),
		},
		"forwarder_metrics": map[string]interface{}{
			"total_requests":   forwarderMetrics.TotalRequests,
			"success_requests": forwarderMetrics.SuccessRequests,
			"failed_requests":  forwarderMetrics.FailedRequests,
			"total_retries":    forwarderMetrics.TotalRetries,
			"average_latency":  fmt.Sprintf("%dms", forwarderMetrics.AverageLatency),
			"success_rate":     fmt.Sprintf("%.2f%%", h.forwarder.GetSuccessRate()),
		},
	}, "Traffic mirror statistics")
}

// Close 关闭消息处理器
func (h *MessageHandler) Close() error {
	h.logManager.LogSystem(logrus.InfoLevel, nil, "Closing message handler")

	// 打印最终统计信息
	h.PrintStats()

	// 关闭转发器
	if err := h.forwarder.Close(); err != nil {
		h.logManager.LogError(logrus.ErrorLevel, logrus.Fields{"error": err.Error()}, "Failed to close forwarder")
		return err
	}

	h.logManager.LogSystem(logrus.InfoLevel, nil, "Message handler closed")
	return nil
}
