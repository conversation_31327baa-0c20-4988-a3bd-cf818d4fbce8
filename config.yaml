# 流量镜像工具配置文件
kafka:
  # Kafka集群地址
  brokers:
    - "10.101.1.105:19092"
  # 消费的topic名称
  topic: "lynxiao_flow"
  # 消费者组ID（手动分区模式下不使用）
  group_id: "traffic-mirror"
  # 并行工作数
  parallel_workers: 20
  # 多消费者配置
  multi_consumer:
    enabled: true  # 启用多消费者模式，每个分区一个消费者
    consumer_count: 0  # 0表示自动根据分区数创建消费者
  # 消费者配置
  consumer:
    # 从最新位置开始消费（只消费应用启动后的新消息）
    offset_initial: "newest"

# 目标API配置
target_api:
  # 目标API地址
  url: "http://10.103.240.54:31021/v1/search"
  # 请求超时时间（秒）
  timeout: 30
  # 重试次数
  retry_count: 3
  # 重试间隔（秒）
  retry_interval: 1

# 过滤配置
filter:
  # 需要处理的消息类型
  message_type: "SearchAPI-Request"
  # 时间过滤配置
  time_filter:
    # 是否启用时间过滤
    enabled: true
    # 时间窗口大小（秒），只处理指定时间内的消息
    window_seconds: 180
    # 时间差容忍度（秒），用于处理Kafka时间戳与服务器时间的差异
    tolerance_seconds: 28800    
    # 时间比较模式：kafka_time（使用Kafka消息时间戳）或 server_time（使用服务器接收时间）
    time_mode: "kafka_time"

# 日志配置
log:
  level: "info"    # 日志级别
  format: "json"   # 日志格式

  # 请求日志（记录处理的消息）
  request:
    enabled: true
    output: "file"
    file_path: "./logs/request.log"
    level: "info"

  # 统计日志（性能监控）
  stats:
    enabled: true
    output: "stdout"
    level: "info"

  # 错误日志
  error:
    enabled: true
    output: "stdout"
    level: "error"

  # 系统日志（启动、停止等）
  system:
    enabled: true
    output: "stdout"
    level: "info"

# 应用配置
app:
  name: "traffic-mirror"
  version: "1.0.0"
  enable_metrics: true    # 启用性能监控
  metrics_interval: 60    # 统计间隔（秒）
