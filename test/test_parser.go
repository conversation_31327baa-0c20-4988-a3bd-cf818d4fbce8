package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/sirupsen/logrus"

	"traffic-mirror/message"
)

func main() {
	// 创建日志记录器
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// 创建消息解析器
	parser := message.NewParser(logger, "SearchAPI-Request")

	// 读取demo.json文件
	data, err := os.ReadFile("../demo_test.json")
	if err != nil {
		log.Fatalf("Failed to read demo.json: %v", err)
	}

	if len(data) == 0 {
		log.Fatalf("demo.json file is empty")
	}

	fmt.Println("=== Testing Message Parser ===")
	fmt.Printf("Input data size: %d bytes\n", len(data))

	// 解析消息
	request, err := parser.ProcessMessage(nil, data)
	if err != nil {
		log.Fatalf("Failed to process message: %v", err)
	}

	if request == nil {
		fmt.Println("Message was skipped (not target type)")
		return
	}

	fmt.Println("\n=== Parsed SearchAPI Request ===")

	// 格式化输出
	requestJSON, err := json.MarshalIndent(request, "", "  ")
	if err != nil {
		log.Fatalf("Failed to marshal request: %v", err)
	}

	fmt.Printf("Parsed request:\n%s\n", string(requestJSON))

	fmt.Println("\n=== Key Information ===")
	fmt.Printf("Trace ID: %s\n", request.Header.TraceID)
	fmt.Printf("App ID: %s\n", request.Header.AppID)
	fmt.Printf("Product Code: %s\n", request.Header.ProdCode)
	fmt.Printf("Intent: %s\n", request.Payload.Intent)
	fmt.Printf("Query: %v\n", request.Payload.Query)

	if request.Parameter.ID != "" {
		fmt.Printf("Parameter ID: %s\n", request.Parameter.ID)
	}
}
