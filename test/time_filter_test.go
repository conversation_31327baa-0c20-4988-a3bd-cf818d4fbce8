package test

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"traffic-mirror/config"
	"traffic-mirror/handler"
	"traffic-mirror/kafka"
	"traffic-mirror/logger"
)

// TestTimeFilterWithinWindow 测试时间过滤功能 - 消息在时间窗口内
func TestTimeFilterWithinWindow(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		Filter: config.FilterConfig{
			MessageType: "SearchAPI-Request",
			TimeFilter: config.TimeFilterConfig{
				Enabled:          true,
				WindowSeconds:    60, // 1分钟窗口
				ToleranceSeconds: 30, // 30秒容忍度
				TimeMode:         "kafka_time",
			},
		},
		TargetAPI: config.TargetAPIConfig{
			URL:           "http://test.example.com",
			Timeout:       30 * time.Second,
			RetryCount:    3,
			RetryInterval: 1 * time.Second,
		},
		Log: config.LogConfig{
			Level:  "debug",
			Format: "json",
			Request: config.RequestLogConfig{
				Enabled: true,
				Output:  "stdout",
				Level:   "debug",
			},
		},
		App: config.AppConfig{
			Name:    "test-app",
			Version: "1.0.0",
		},
	}

	// 创建日志管理器
	logManager, err := logger.NewLoggerManager(cfg)
	assert.NoError(t, err)

	// 创建消息处理器
	messageHandler, err := handler.NewMessageHandler(cfg, logManager)
	assert.NoError(t, err)

	// 创建测试消息 - 当前时间的消息（应该被处理）
	now := time.Now()
	message := &kafka.Message{
		Topic:     "test-topic",
		Partition: 0,
		Offset:    123,
		Key:       []byte("test-key"),
		Value:     []byte(`{"data": "{\"header\":{\"appId\":\"test123\",\"prodCode\":\"HealthySearch\",\"traceId\":\"trace123\"},\"parameter\":{\"id\":\"param123\"},\"payload\":{\"appId\":\"test123\",\"intent\":\"2\",\"query\":[\"测试查询\"]}}"}`),
		Time:      now,
		MessageID: "test-topic-0-123",
	}

	// 测试消息处理
	err = messageHandler.HandleMessage(nil, message)
	assert.NoError(t, err)

	// 验证统计指标
	metrics := messageHandler.GetMetrics()
	assert.Equal(t, int64(1), metrics.TotalMessages)
	assert.Equal(t, int64(1), metrics.ProcessedMessages)
	assert.Equal(t, int64(0), metrics.SkippedMessages)
}

// TestTimeFilterOutsideWindow 测试时间过滤功能 - 消息在时间窗口外
func TestTimeFilterOutsideWindow(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		Filter: config.FilterConfig{
			MessageType: "SearchAPI-Request",
			TimeFilter: config.TimeFilterConfig{
				Enabled:          true,
				WindowSeconds:    60, // 1分钟窗口
				ToleranceSeconds: 10, // 10秒容忍度
				TimeMode:         "kafka_time",
			},
		},
		TargetAPI: config.TargetAPIConfig{
			URL:           "http://test.example.com",
			Timeout:       30 * time.Second,
			RetryCount:    3,
			RetryInterval: 1 * time.Second,
		},
		Log: config.LogConfig{
			Level:  "debug",
			Format: "json",
			Request: config.RequestLogConfig{
				Enabled: true,
				Output:  "stdout",
				Level:   "debug",
			},
		},
		App: config.AppConfig{
			Name:    "test-app",
			Version: "1.0.0",
		},
	}

	// 创建日志管理器
	logManager, err := logger.NewLoggerManager(cfg)
	assert.NoError(t, err)

	// 创建消息处理器
	messageHandler, err := handler.NewMessageHandler(cfg, logManager)
	assert.NoError(t, err)

	// 创建测试消息 - 2分钟前的消息（应该被跳过）
	oldTime := time.Now().Add(-2 * time.Minute)
	message := &kafka.Message{
		Topic:     "test-topic",
		Partition: 0,
		Offset:    124,
		Key:       []byte("test-key"),
		Value:     []byte(`{"data": "{\"header\":{\"appId\":\"test123\",\"prodCode\":\"HealthySearch\",\"traceId\":\"trace124\"},\"parameter\":{\"id\":\"param124\"},\"payload\":{\"appId\":\"test123\",\"intent\":\"2\",\"query\":[\"测试查询\"]}}"}`),
		Time:      oldTime,
		MessageID: "test-topic-0-124",
	}

	// 测试消息处理
	err = messageHandler.HandleMessage(nil, message)
	assert.NoError(t, err)

	// 验证统计指标
	metrics := messageHandler.GetMetrics()
	assert.Equal(t, int64(1), metrics.TotalMessages)
	assert.Equal(t, int64(0), metrics.ProcessedMessages)
	assert.Equal(t, int64(1), metrics.SkippedMessages)
}

// TestTimeFilterDisabled 测试时间过滤功能禁用时的行为
func TestTimeFilterDisabled(t *testing.T) {
	// 创建测试配置 - 禁用时间过滤
	cfg := &config.Config{
		Filter: config.FilterConfig{
			MessageType: "SearchAPI-Request",
			TimeFilter: config.TimeFilterConfig{
				Enabled:          false, // 禁用时间过滤
				WindowSeconds:    60,
				ToleranceSeconds: 30,
				TimeMode:         "kafka_time",
			},
		},
		TargetAPI: config.TargetAPIConfig{
			URL:           "http://test.example.com",
			Timeout:       30 * time.Second,
			RetryCount:    3,
			RetryInterval: 1 * time.Second,
		},
		Log: config.LogConfig{
			Level:  "debug",
			Format: "json",
			Request: config.RequestLogConfig{
				Enabled: true,
				Output:  "stdout",
				Level:   "debug",
			},
		},
		App: config.AppConfig{
			Name:    "test-app",
			Version: "1.0.0",
		},
	}

	// 创建日志管理器
	logManager, err := logger.NewLoggerManager(cfg)
	assert.NoError(t, err)

	// 创建消息处理器
	messageHandler, err := handler.NewMessageHandler(cfg, logManager)
	assert.NoError(t, err)

	// 创建测试消息 - 很久以前的消息（应该被处理，因为时间过滤被禁用）
	veryOldTime := time.Now().Add(-10 * time.Minute)
	message := &kafka.Message{
		Topic:     "test-topic",
		Partition: 0,
		Offset:    125,
		Key:       []byte("test-key"),
		Value:     []byte(`{"data": "{\"header\":{\"appId\":\"test123\",\"prodCode\":\"HealthySearch\",\"traceId\":\"trace125\"},\"parameter\":{\"id\":\"param125\"},\"payload\":{\"appId\":\"test123\",\"intent\":\"2\",\"query\":[\"测试查询\"]}}"}`),
		Time:      veryOldTime,
		MessageID: "test-topic-0-125",
	}

	// 测试消息处理
	err = messageHandler.HandleMessage(nil, message)
	assert.NoError(t, err)

	// 验证统计指标 - 即使消息很旧，也应该被处理
	metrics := messageHandler.GetMetrics()
	assert.Equal(t, int64(1), metrics.TotalMessages)
	assert.Equal(t, int64(1), metrics.ProcessedMessages)
	assert.Equal(t, int64(0), metrics.SkippedMessages)
}

// TestTimeFilterServerTimeMode 测试服务器时间模式
func TestTimeFilterServerTimeMode(t *testing.T) {
	// 创建测试配置 - 使用服务器时间模式
	cfg := &config.Config{
		Filter: config.FilterConfig{
			MessageType: "SearchAPI-Request",
			TimeFilter: config.TimeFilterConfig{
				Enabled:          true,
				WindowSeconds:    60,
				ToleranceSeconds: 30,
				TimeMode:         "server_time", // 使用服务器时间
			},
		},
		TargetAPI: config.TargetAPIConfig{
			URL:           "http://test.example.com",
			Timeout:       30 * time.Second,
			RetryCount:    3,
			RetryInterval: 1 * time.Second,
		},
		Log: config.LogConfig{
			Level:  "debug",
			Format: "json",
			Request: config.RequestLogConfig{
				Enabled: true,
				Output:  "stdout",
				Level:   "debug",
			},
		},
		App: config.AppConfig{
			Name:    "test-app",
			Version: "1.0.0",
		},
	}

	// 创建日志管理器
	logManager, err := logger.NewLoggerManager(cfg)
	assert.NoError(t, err)

	// 创建消息处理器
	messageHandler, err := handler.NewMessageHandler(cfg, logManager)
	assert.NoError(t, err)

	// 创建测试消息 - 即使Kafka时间很旧，但使用服务器时间模式，应该被处理
	veryOldTime := time.Now().Add(-10 * time.Minute)
	message := &kafka.Message{
		Topic:     "test-topic",
		Partition: 0,
		Offset:    126,
		Key:       []byte("test-key"),
		Value:     []byte(`{"data": "{\"header\":{\"appId\":\"test123\",\"prodCode\":\"HealthySearch\",\"traceId\":\"trace126\"},\"parameter\":{\"id\":\"param126\"},\"payload\":{\"appId\":\"test123\",\"intent\":\"2\",\"query\":[\"测试查询\"]}}"}`),
		Time:      veryOldTime,
		MessageID: "test-topic-0-126",
	}

	// 测试消息处理
	err = messageHandler.HandleMessage(nil, message)
	assert.NoError(t, err)

	// 验证统计指标 - 应该被处理，因为使用服务器时间模式
	metrics := messageHandler.GetMetrics()
	assert.Equal(t, int64(1), metrics.TotalMessages)
	assert.Equal(t, int64(1), metrics.ProcessedMessages)
	assert.Equal(t, int64(0), metrics.SkippedMessages)
}

// TestTimeFilterPrecisionAnalysis 测试时间过滤精度分析
func TestTimeFilterPrecisionAnalysis(t *testing.T) {
	fmt.Println("\n=== 时间过滤精度分析测试 ===")

	// 测试不同的时间差异场景
	testCases := []struct {
		name           string
		timeOffset     time.Duration
		tolerance      int
		expectedResult bool
		description    string
	}{
		{"当前时间", 0, 0, true, "应该被处理"},
		{"30秒前-零容忍", -30 * time.Second, 0, true, "在60秒窗口内，应该被处理"},
		{"60秒前-零容忍", -60 * time.Second, 0, false, "刚好60秒，应该被跳过"},
		{"61秒前-零容忍", -61 * time.Second, 0, false, "超过60秒，应该被跳过"},
		{"69秒前-10秒容忍", -69 * time.Second, 10, true, "69秒但有10秒容忍，应该被处理"},
		{"71秒前-10秒容忍", -71 * time.Second, 10, false, "71秒超过容忍度，应该被跳过"},
		{"未来30秒-零容忍", 30 * time.Second, 0, true, "未来时间在窗口内，应该被处理"},
		{"未来61秒-零容忍", 61 * time.Second, 0, false, "未来时间超过窗口，应该被跳过"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			fmt.Printf("\n🔍 测试场景: %s\n", tc.name)

			// 创建测试配置
			cfg := &config.Config{
				Filter: config.FilterConfig{
					MessageType: "SearchAPI-Request",
					TimeFilter: config.TimeFilterConfig{
						Enabled:          true,
						WindowSeconds:    60,
						ToleranceSeconds: tc.tolerance,
						TimeMode:         "kafka_time",
					},
				},
				TargetAPI: config.TargetAPIConfig{
					URL:           "http://test.example.com",
					Timeout:       5 * time.Second,
					RetryCount:    1,
					RetryInterval: 1 * time.Second,
				},
				Log: config.LogConfig{
					Level:  "debug",
					Format: "json",
					Request: config.RequestLogConfig{
						Enabled: true,
						Output:  "stdout",
						Level:   "debug",
					},
				},
				App: config.AppConfig{
					Name:    "precision-test",
					Version: "1.0.0",
				},
			}

			// 创建日志管理器
			logManager, err := logger.NewLoggerManager(cfg)
			assert.NoError(t, err)

			// 创建消息处理器
			messageHandler, err := handler.NewMessageHandler(cfg, logManager)
			assert.NoError(t, err)

			// 创建测试消息
			kafkaTime := time.Now().Add(tc.timeOffset)
			message := &kafka.Message{
				Topic:     "precision-test-topic",
				Partition: 0,
				Offset:    int64(time.Now().UnixNano()),
				Key:       []byte("precision-test-key"),
				Value:     []byte(`{"data": "{\"header\":{\"appId\":\"precision123\",\"prodCode\":\"HealthySearch\",\"traceId\":\"precision-trace\"},\"parameter\":{\"id\":\"precision-param\"},\"payload\":{\"appId\":\"precision123\",\"intent\":\"2\",\"query\":[\"精度测试\"]}}"}`),
				Time:      kafkaTime,
				MessageID: fmt.Sprintf("precision-%d", time.Now().UnixNano()),
			}

			// 记录初始指标
			initialMetrics := messageHandler.GetMetrics()

			// 计算时间差异
			serverTime := time.Now()
			timeDiff := serverTime.Sub(kafkaTime)

			fmt.Printf("   Kafka时间: %s\n", kafkaTime.Format("2006-01-02 15:04:05.000"))
			fmt.Printf("   服务器时间: %s\n", serverTime.Format("2006-01-02 15:04:05.000"))
			fmt.Printf("   时间差异: %d毫秒\n", timeDiff.Milliseconds())
			fmt.Printf("   窗口大小: %d秒\n", cfg.Filter.TimeFilter.WindowSeconds)
			fmt.Printf("   容忍度: %d秒\n", cfg.Filter.TimeFilter.ToleranceSeconds)
			fmt.Printf("   预期结果: %s\n", tc.description)

			// 处理消息
			err = messageHandler.HandleMessage(nil, message)
			assert.NoError(t, err)

			// 获取最终指标
			finalMetrics := messageHandler.GetMetrics()

			// 验证结果
			wasProcessed := finalMetrics.ProcessedMessages > initialMetrics.ProcessedMessages
			wasSkipped := finalMetrics.SkippedMessages > initialMetrics.SkippedMessages

			if tc.expectedResult {
				assert.True(t, wasProcessed, "消息应该被处理")
				assert.False(t, wasSkipped, "消息不应该被跳过")
				fmt.Printf("   ✅ 实际结果: 消息被处理\n")
			} else {
				assert.False(t, wasProcessed, "消息不应该被处理")
				assert.True(t, wasSkipped, "消息应该被跳过")
				fmt.Printf("   ❌ 实际结果: 消息被跳过\n")
			}
		})
	}

	fmt.Println("\n=== 时间过滤精度分析完成 ===")
}
