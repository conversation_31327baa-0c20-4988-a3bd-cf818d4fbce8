package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Kafka     KafkaConfig     `yaml:"kafka"`
	TargetAPI TargetAPIConfig `yaml:"target_api"`
	Filter    FilterConfig    `yaml:"filter"`
	Log       LogConfig       `yaml:"log"`
	App       AppConfig       `yaml:"app"`
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	Brokers         []string            `yaml:"brokers"`
	Topic           string              `yaml:"topic"`
	GroupID         string              `yaml:"group_id"`
	Consumer        ConsumerConfig      `yaml:"consumer"`
	ParallelWorkers int                 `yaml:"parallel_workers"` // 并行工作数
	MultiConsumer   MultiConsumerConfig `yaml:"multi_consumer"`   // 多消费者配置
}

// MultiConsumerConfig 多消费者配置
type MultiConsumerConfig struct {
	Enabled       bool `yaml:"enabled"`        // 是否启用多消费者模式
	ConsumerCount int  `yaml:"consumer_count"` // 消费者数量（0表示自动计算）
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	OffsetInitial     string `yaml:"offset_initial"`
	SessionTimeout    int    `yaml:"session_timeout"`
	HeartbeatInterval int    `yaml:"heartbeat_interval"`
}

// TargetAPIConfig 目标API配置
type TargetAPIConfig struct {
	URL              string        `yaml:"url"`
	Timeout          time.Duration `yaml:"-"`
	TimeoutSec       int           `yaml:"timeout"`
	RetryCount       int           `yaml:"retry_count"`
	RetryInterval    time.Duration `yaml:"-"`
	RetryIntervalSec int           `yaml:"retry_interval"`
}

// FilterConfig 过滤配置
type FilterConfig struct {
	MessageType string `yaml:"message_type"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level   string           `yaml:"level"`
	Format  string           `yaml:"format"`
	Request RequestLogConfig `yaml:"request"`
	Stats   StatsLogConfig   `yaml:"stats"`
	Error   ErrorLogConfig   `yaml:"error"`
	System  SystemLogConfig  `yaml:"system"`
}

// RequestLogConfig 请求日志配置
type RequestLogConfig struct {
	Enabled  bool   `yaml:"enabled"`
	Output   string `yaml:"output"` // "file", "stdout", "both"
	FilePath string `yaml:"file_path"`
	Level    string `yaml:"level"`
}

// StatsLogConfig 统计日志配置
type StatsLogConfig struct {
	Enabled bool   `yaml:"enabled"`
	Output  string `yaml:"output"` // "stdout", "file", "both"
	Level   string `yaml:"level"`
}

// ErrorLogConfig 错误日志配置
type ErrorLogConfig struct {
	Enabled  bool   `yaml:"enabled"`
	Output   string `yaml:"output"` // "stdout", "file", "both"
	FilePath string `yaml:"file_path"`
	Level    string `yaml:"level"`
}

// SystemLogConfig 系统日志配置
type SystemLogConfig struct {
	Enabled bool   `yaml:"enabled"`
	Output  string `yaml:"output"` // "stdout", "file", "both"
	Level   string `yaml:"level"`
}

// AppConfig 应用配置
type AppConfig struct {
	Name            string `yaml:"name"`
	Version         string `yaml:"version"`
	EnableMetrics   bool   `yaml:"enable_metrics"`
	MetricsInterval int    `yaml:"metrics_interval"`
}

// LoadConfig 从文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析YAML配置
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 转换时间单位
	config.TargetAPI.Timeout = time.Duration(config.TargetAPI.TimeoutSec) * time.Second

	// 应用环境变量覆盖
	config.applyEnvironmentOverrides()

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return &config, nil
}

// applyEnvironmentOverrides 应用环境变量覆盖配置
func (c *Config) applyEnvironmentOverrides() {
	// Kafka配置
	if brokers := os.Getenv("KAFKA_BROKERS"); brokers != "" {
		c.Kafka.Brokers = strings.Split(brokers, ",")
	}
	if topic := os.Getenv("KAFKA_TOPIC"); topic != "" {
		c.Kafka.Topic = topic
	}
	if groupID := os.Getenv("KAFKA_GROUP_ID"); groupID != "" {
		c.Kafka.GroupID = groupID
	}
	if workersStr := os.Getenv("KAFKA_PARALLEL_WORKERS"); workersStr != "" {
		if workers, err := strconv.Atoi(workersStr); err == nil && workers > 0 {
			c.Kafka.ParallelWorkers = workers
		}
	}

	// 多消费者配置
	if enabledStr := os.Getenv("KAFKA_MULTI_CONSUMER_ENABLED"); enabledStr != "" {
		if enabled, err := strconv.ParseBool(enabledStr); err == nil {
			c.Kafka.MultiConsumer.Enabled = enabled
		}
	}
	if countStr := os.Getenv("KAFKA_MULTI_CONSUMER_COUNT"); countStr != "" {
		if count, err := strconv.Atoi(countStr); err == nil && count >= 0 {
			c.Kafka.MultiConsumer.ConsumerCount = count
		}
	}

	// 目标API配置
	if url := os.Getenv("TARGET_API_URL"); url != "" {
		c.TargetAPI.URL = url
	}
	if timeoutStr := os.Getenv("TARGET_API_TIMEOUT"); timeoutStr != "" {
		if timeout, err := strconv.Atoi(timeoutStr); err == nil {
			c.TargetAPI.TimeoutSec = timeout
			c.TargetAPI.Timeout = time.Duration(timeout) * time.Second
		}
	}

	// 过滤配置
	if messageType := os.Getenv("MESSAGE_TYPE"); messageType != "" {
		c.Filter.MessageType = messageType
	}

	// 日志配置
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		c.Log.Level = logLevel
	}
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	// 验证Kafka配置
	if len(c.Kafka.Brokers) == 0 {
		return fmt.Errorf("kafka brokers cannot be empty")
	}
	if c.Kafka.Topic == "" {
		return fmt.Errorf("kafka topic cannot be empty")
	}
	if c.Kafka.GroupID == "" {
		return fmt.Errorf("kafka group_id cannot be empty")
	}

	// 验证目标API配置
	if c.TargetAPI.URL == "" {
		return fmt.Errorf("target_api url cannot be empty")
	}
	if c.TargetAPI.Timeout <= 0 {
		return fmt.Errorf("target_api timeout must be positive")
	}

	// 验证过滤配置
	if c.Filter.MessageType == "" {
		return fmt.Errorf("filter message_type cannot be empty")
	}

	// 验证日志配置
	validLogLevels := map[string]bool{
		"debug": true, "info": true, "warn": true, "error": true,
	}
	if !validLogLevels[c.Log.Level] {
		return fmt.Errorf("invalid log level: %s", c.Log.Level)
	}

	validLogFormats := map[string]bool{
		"json": true, "text": true,
	}
	if !validLogFormats[c.Log.Format] {
		return fmt.Errorf("invalid log format: %s", c.Log.Format)
	}

	validLogOutputs := map[string]bool{
		"stdout": true, "file": true, "both": true,
	}

	// 验证请求日志配置
	if c.Log.Request.Enabled {
		if !validLogOutputs[c.Log.Request.Output] {
			return fmt.Errorf("invalid request log output: %s", c.Log.Request.Output)
		}
		if !validLogLevels[c.Log.Request.Level] {
			return fmt.Errorf("invalid request log level: %s", c.Log.Request.Level)
		}
		if (c.Log.Request.Output == "file" || c.Log.Request.Output == "both") && c.Log.Request.FilePath == "" {
			return fmt.Errorf("request log file path cannot be empty when output is file or both")
		}
	}

	// 验证统计日志配置
	if c.Log.Stats.Enabled {
		if !validLogOutputs[c.Log.Stats.Output] {
			return fmt.Errorf("invalid stats log output: %s", c.Log.Stats.Output)
		}
		if !validLogLevels[c.Log.Stats.Level] {
			return fmt.Errorf("invalid stats log level: %s", c.Log.Stats.Level)
		}
	}

	// 验证错误日志配置
	if c.Log.Error.Enabled {
		if !validLogOutputs[c.Log.Error.Output] {
			return fmt.Errorf("invalid error log output: %s", c.Log.Error.Output)
		}
		if !validLogLevels[c.Log.Error.Level] {
			return fmt.Errorf("invalid error log level: %s", c.Log.Error.Level)
		}
		if (c.Log.Error.Output == "file" || c.Log.Error.Output == "both") && c.Log.Error.FilePath == "" {
			return fmt.Errorf("error log file path cannot be empty when output is file or both")
		}
	}

	// 验证系统日志配置
	if c.Log.System.Enabled {
		if !validLogOutputs[c.Log.System.Output] {
			return fmt.Errorf("invalid system log output: %s", c.Log.System.Output)
		}
		if !validLogLevels[c.Log.System.Level] {
			return fmt.Errorf("invalid system log level: %s", c.Log.System.Level)
		}
	}

	return nil
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return &Config{
		Kafka: KafkaConfig{
			Brokers:         []string{"10.101.1.105:19092"},
			Topic:           "lynxiao_flow",
			GroupID:         "traffic-mirror-consumer",
			ParallelWorkers: 10, // 默认10个并行工作者
			Consumer: ConsumerConfig{
				OffsetInitial:     "newest",
				SessionTimeout:    30,
				HeartbeatInterval: 3,
			},
			MultiConsumer: MultiConsumerConfig{
				Enabled:       false, // 默认关闭多消费者模式
				ConsumerCount: 0,     // 自动计算
			},
		},
		TargetAPI: TargetAPIConfig{
			URL:           "http://0.0.0.0:50409/v1/search",
			Timeout:       30 * time.Second,
			RetryCount:    3,
			RetryInterval: 1 * time.Second,
		},
		Filter: FilterConfig{
			MessageType: "SearchAPI-Request",
		},
		Log: LogConfig{
			Level:  "info",
			Format: "json",
			Request: RequestLogConfig{
				Enabled:  true,
				Output:   "file",
				FilePath: "./logs/request.log",
				Level:    "info",
			},
			Stats: StatsLogConfig{
				Enabled: true,
				Output:  "stdout",
				Level:   "info",
			},
			Error: ErrorLogConfig{
				Enabled:  true,
				Output:   "both",
				FilePath: "./logs/error.log",
				Level:    "error",
			},
			System: SystemLogConfig{
				Enabled: true,
				Output:  "stdout",
				Level:   "info",
			},
		},
		App: AppConfig{
			Name:            "traffic-mirror",
			Version:         "1.0.0",
			EnableMetrics:   true,
			MetricsInterval: 60,
		},
	}
}
