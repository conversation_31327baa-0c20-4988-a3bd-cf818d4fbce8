package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"

	"traffic-mirror/config"
)

// LoggerManager 日志管理器
type LoggerManager struct {
	RequestLogger *logrus.Logger
	StatsLogger   *logrus.Logger
	ErrorLogger   *logrus.Logger
	SystemLogger  *logrus.Logger
	config        *config.Config
}

// LogType 日志类型
type LogType int

const (
	LogTypeRequest LogType = iota
	LogTypeStats
	LogTypeError
	LogTypeSystem
)

// NewLoggerManager 创建新的日志管理器
func NewLoggerManager(cfg *config.Config) (*LoggerManager, error) {
	manager := &LoggerManager{
		config: cfg,
	}

	var err error

	// 创建请求日志器
	if cfg.Log.Request.Enabled {
		manager.RequestLogger, err = createLogger(cfg, cfg.Log.Request.Level, cfg.Log.Request.Output, cfg.Log.Request.FilePath)
		if err != nil {
			return nil, fmt.Errorf("failed to create request logger: %w", err)
		}
	}

	// 创建统计日志器
	if cfg.Log.Stats.Enabled {
		manager.StatsLogger, err = createLogger(cfg, cfg.Log.Stats.Level, cfg.Log.Stats.Output, "")
		if err != nil {
			return nil, fmt.Errorf("failed to create stats logger: %w", err)
		}
	}

	// 创建错误日志器
	if cfg.Log.Error.Enabled {
		manager.ErrorLogger, err = createLogger(cfg, cfg.Log.Error.Level, cfg.Log.Error.Output, cfg.Log.Error.FilePath)
		if err != nil {
			return nil, fmt.Errorf("failed to create error logger: %w", err)
		}
	}

	// 创建系统日志器
	if cfg.Log.System.Enabled {
		manager.SystemLogger, err = createLogger(cfg, cfg.Log.System.Level, cfg.Log.System.Output, "")
		if err != nil {
			return nil, fmt.Errorf("failed to create system logger: %w", err)
		}
	}

	return manager, nil
}

// GetLogger 根据类型获取对应的日志器
func (lm *LoggerManager) GetLogger(logType LogType) *logrus.Logger {
	switch logType {
	case LogTypeRequest:
		return lm.RequestLogger
	case LogTypeStats:
		return lm.StatsLogger
	case LogTypeError:
		return lm.ErrorLogger
	case LogTypeSystem:
		return lm.SystemLogger
	default:
		return lm.SystemLogger // 默认返回系统日志器
	}
}

// createRotatingFileWriter 创建带轮转的文件写入器
func createRotatingFileWriter(filePath string) io.Writer {
	return &lumberjack.Logger{
		Filename:   filePath,
		MaxSize:    500,  // 每个日志文件最大100MB
		MaxBackups: 7,    // 保留7个备份文件
		MaxAge:     7,    // 保留7天
		Compress:   true, // 压缩旧文件
		LocalTime:  true, // 使用本地时间
	}
}

// createLogger 创建单个日志器
func createLogger(cfg *config.Config, level, output, filePath string) (*logrus.Logger, error) {
	logger := logrus.New()

	// 设置日志级别
	logLevel, err := logrus.ParseLevel(level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level: %w", err)
	}
	logger.SetLevel(logLevel)

	// 设置日志格式
	switch strings.ToLower(cfg.Log.Format) {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FieldMap: logrus.FieldMap{
				logrus.FieldKeyTime:  "timestamp",
				logrus.FieldKeyLevel: "level",
				logrus.FieldKeyMsg:   "message",
				logrus.FieldKeyFunc:  "function",
			},
		})
	case "text":
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	default:
		return nil, fmt.Errorf("unsupported log format: %s", cfg.Log.Format)
	}

	// 设置日志输出
	switch strings.ToLower(output) {
	case "stdout":
		logger.SetOutput(os.Stdout)
	case "file":
		if filePath == "" {
			return nil, fmt.Errorf("file path cannot be empty when output is file")
		}
		if err := ensureLogDir(filePath); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}
		// 使用带轮转的文件写入器
		rotatingWriter := createRotatingFileWriter(filePath)
		logger.SetOutput(rotatingWriter)
	case "both":
		if filePath == "" {
			return nil, fmt.Errorf("file path cannot be empty when output is both")
		}
		if err := ensureLogDir(filePath); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}
		// 使用带轮转的文件写入器，同时输出到控制台
		rotatingWriter := createRotatingFileWriter(filePath)
		logger.SetOutput(io.MultiWriter(os.Stdout, rotatingWriter))
	default:
		return nil, fmt.Errorf("unsupported log output: %s", output)
	}

	// 添加应用信息到所有日志
	logger = logger.WithFields(logrus.Fields{
		"app":     cfg.App.Name,
		"version": cfg.App.Version,
	}).Logger

	return logger, nil
}

// SetupLogger 设置日志记录器（保持向后兼容）
// 已弃用：请使用 NewLoggerManager 代替
func SetupLogger(cfg *config.Config) (*logrus.Logger, error) {
	// 为了向后兼容，创建一个基于系统日志配置的logger
	if cfg.Log.System.Enabled {
		return createLogger(cfg, cfg.Log.System.Level, cfg.Log.System.Output, "")
	}

	// 如果系统日志未启用，创建一个默认的stdout logger
	return createLogger(cfg, cfg.Log.Level, "stdout", "")
}

// LogRequest 记录请求日志
func (lm *LoggerManager) LogRequest(level logrus.Level, fields logrus.Fields, message string) {
	if lm.RequestLogger != nil {
		LogWithContext(lm.RequestLogger, level, fields, message)
	}
}

// LogStats 记录统计日志
func (lm *LoggerManager) LogStats(level logrus.Level, fields logrus.Fields, message string) {
	if lm.StatsLogger != nil {
		LogWithContext(lm.StatsLogger, level, fields, message)
	}
}

// LogError 记录错误日志
func (lm *LoggerManager) LogError(level logrus.Level, fields logrus.Fields, message string) {
	if lm.ErrorLogger != nil {
		LogWithContext(lm.ErrorLogger, level, fields, message)
	}
}

// LogSystem 记录系统日志
func (lm *LoggerManager) LogSystem(level logrus.Level, fields logrus.Fields, message string) {
	if lm.SystemLogger != nil {
		LogWithContext(lm.SystemLogger, level, fields, message)
	}
}

// ensureLogDir 确保日志目录存在
func ensureLogDir(logPath string) error {
	dir := filepath.Dir(logPath)
	if dir == "." {
		return nil
	}

	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}

	return nil
}

// LogWithContext 带上下文的日志记录
func LogWithContext(logger *logrus.Logger, level logrus.Level, fields logrus.Fields, message string) {
	entry := logger.WithFields(fields)

	switch level {
	case logrus.DebugLevel:
		entry.Debug(message)
	case logrus.InfoLevel:
		entry.Info(message)
	case logrus.WarnLevel:
		entry.Warn(message)
	case logrus.ErrorLevel:
		entry.Error(message)
	case logrus.FatalLevel:
		entry.Fatal(message)
	case logrus.PanicLevel:
		entry.Panic(message)
	default:
		entry.Info(message)
	}
}
