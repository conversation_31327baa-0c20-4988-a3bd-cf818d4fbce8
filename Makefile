# 流量镜像工具 Makefile
# 用于简化构建、测试和发布流程

# 应用信息
APP_NAME := traffic-mirror
VERSION := $(shell cat VERSION 2>/dev/null || echo "1.0.0")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
GO_VERSION := $(shell go version | awk '{print $$3}')

# 构建标志
LDFLAGS := -X main.Version=$(VERSION) \
           -X main.BuildTime=$(BUILD_TIME) \
           -X main.GitCommit=$(GIT_COMMIT) \
           -X main.GoVersion=$(GO_VERSION)

# 目录
BUILD_DIR := build
DIST_DIR := $(BUILD_DIR)/dist
PACKAGES_DIR := $(BUILD_DIR)/packages
RELEASE_DIR := releases

# 支持的平台
PLATFORMS := linux/amd64 linux/arm64 darwin/amd64 darwin/arm64 windows/amd64

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m

##@ 帮助

.PHONY: help
help: ## 显示帮助信息
	@echo "流量镜像工具构建系统"
	@echo ""
	@awk 'BEGIN {FS = ":.*##"; printf "\n使用方法:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ 开发

.PHONY: deps
deps: ## 安装依赖
	@echo "$(GREEN)[INFO]$(NC) 安装Go依赖..."
	go mod tidy
	go mod download

.PHONY: fmt
fmt: ## 格式化代码
	@echo "$(GREEN)[INFO]$(NC) 格式化代码..."
	go fmt ./...

.PHONY: vet
vet: ## 代码静态检查
	@echo "$(GREEN)[INFO]$(NC) 运行代码静态检查..."
	go vet ./...

.PHONY: lint
lint: ## 代码风格检查 (需要golangci-lint)
	@echo "$(GREEN)[INFO]$(NC) 运行代码风格检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "$(YELLOW)[WARN]$(NC) golangci-lint 未安装，跳过检查"; \
	fi

.PHONY: test
test: ## 运行测试
	@echo "$(GREEN)[INFO]$(NC) 运行测试..."
	@if find . -name "*_test.go" -not -path "./test/*" | grep -q .; then \
		go test -v $$(go list ./... | grep -v '/test'); \
	else \
		echo "$(YELLOW)[WARN]$(NC) 未发现正式测试文件"; \
	fi

.PHONY: test-coverage
test-coverage: ## 运行测试并生成覆盖率报告
	@echo "$(GREEN)[INFO]$(NC) 运行测试覆盖率检查..."
	@if find . -name "*_test.go" -not -path "./test/*" | grep -q .; then \
		go test -v -coverprofile=coverage.out $$(go list ./... | grep -v '/test'); \
		go tool cover -html=coverage.out -o coverage.html; \
		echo "$(GREEN)[INFO]$(NC) 覆盖率报告生成: coverage.html"; \
	else \
		echo "$(YELLOW)[WARN]$(NC) 未发现正式测试文件"; \
	fi

##@ 构建

.PHONY: build
build: deps ## 构建本地平台二进制文件
	@echo "$(GREEN)[INFO]$(NC) 构建本地平台二进制文件..."
	CGO_ENABLED=0 go build -ldflags "$(LDFLAGS)" -o $(APP_NAME) .

.PHONY: build-linux
build-linux: deps ## 构建Linux平台二进制文件
	@echo "$(GREEN)[INFO]$(NC) 构建Linux平台二进制文件..."
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "$(LDFLAGS)" -o $(APP_NAME)-linux-amd64 .

.PHONY: build-all
build-all: deps ## 构建所有平台二进制文件
	@echo "$(GREEN)[INFO]$(NC) 构建所有平台二进制文件..."
	@./build.sh

.PHONY: clean
clean: ## 清理构建文件
	@echo "$(GREEN)[INFO]$(NC) 清理构建文件..."
	rm -rf $(BUILD_DIR)
	rm -f $(APP_NAME) $(APP_NAME)-*
	rm -f coverage.out coverage.html

##@ 测试环境

.PHONY: dev
dev: build ## 使用开发配置运行
	@echo "$(GREEN)[INFO]$(NC) 使用开发配置运行..."
	./$(APP_NAME) -config config_dev.yaml

.PHONY: mock-server
mock-server: ## 启动模拟API服务器
	@echo "$(GREEN)[INFO]$(NC) 启动模拟API服务器..."
	cd test && go run mock_server.go

.PHONY: test-components
test-components: ## 运行组件测试
	@echo "$(GREEN)[INFO]$(NC) 运行组件测试..."
	@if [ -d "test" ]; then \
		cd test && \
		echo "测试消息解析器..." && go run test_parser.go && \
		echo "测试HTTP转发器..." && go run test_forwarder.go && \
		echo "测试消息处理器..." && go run test_handler.go; \
	else \
		echo "$(YELLOW)[WARN]$(NC) 测试目录不存在"; \
	fi

##@ 部署

.PHONY: install
install: build ## 本地安装 (需要root权限)
	@echo "$(GREEN)[INFO]$(NC) 本地安装..."
	sudo ./deploy.sh

.PHONY: quick-install
quick-install: build ## 快速安装 (使用quick-deploy脚本)
	@echo "$(GREEN)[INFO]$(NC) 快速安装..."
	sudo ./quick-deploy.sh

.PHONY: uninstall
uninstall: ## 卸载应用
	@echo "$(GREEN)[INFO]$(NC) 卸载应用..."
	sudo ./quick-deploy.sh --uninstall

.PHONY: status
status: ## 检查应用状态
	@echo "$(GREEN)[INFO]$(NC) 检查应用状态..."
	@if [ -f "./check_status.sh" ]; then \
		./check_status.sh; \
	else \
		systemctl status traffic-mirror; \
	fi

##@ 发布

.PHONY: package
package: build-all ## 创建发行包
	@echo "$(GREEN)[INFO]$(NC) 创建发行包..."
	@./build.sh

.PHONY: release-patch
release-patch: ## 发布补丁版本 (x.y.z -> x.y.z+1)
	@echo "$(GREEN)[INFO]$(NC) 发布补丁版本..."
	./release.sh --patch

.PHONY: release-minor
release-minor: ## 发布次版本 (x.y.z -> x.y+1.0)
	@echo "$(GREEN)[INFO]$(NC) 发布次版本..."
	./release.sh --minor

.PHONY: release-major
release-major: ## 发布主版本 (x.y.z -> x+1.0.0)
	@echo "$(GREEN)[INFO]$(NC) 发布主版本..."
	./release.sh --major

.PHONY: release
release: ## 发布指定版本 (使用: make release VERSION=x.y.z)
	@if [ -z "$(VERSION)" ]; then \
		echo "$(YELLOW)[ERROR]$(NC) 请指定版本号: make release VERSION=x.y.z"; \
		exit 1; \
	fi
	@echo "$(GREEN)[INFO]$(NC) 发布版本 $(VERSION)..."
	./release.sh $(VERSION)

.PHONY: release-dry-run
release-dry-run: ## 预览发布过程
	@echo "$(GREEN)[INFO]$(NC) 预览发布过程..."
	./release.sh --dry-run --patch

##@ 维护

.PHONY: update-deps
update-deps: ## 更新依赖
	@echo "$(GREEN)[INFO]$(NC) 更新Go依赖..."
	go get -u ./...
	go mod tidy

.PHONY: security-check
security-check: ## 安全检查 (需要gosec)
	@echo "$(GREEN)[INFO]$(NC) 运行安全检查..."
	@if command -v gosec >/dev/null 2>&1; then \
		gosec ./...; \
	else \
		echo "$(YELLOW)[WARN]$(NC) gosec 未安装，跳过安全检查"; \
		echo "安装命令: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

.PHONY: benchmark
benchmark: ## 运行性能测试
	@echo "$(GREEN)[INFO]$(NC) 运行性能测试..."
	go test -bench=. -benchmem ./...

.PHONY: profile
profile: build ## 生成性能分析文件
	@echo "$(GREEN)[INFO]$(NC) 生成性能分析文件..."
	@echo "启动应用进行性能分析..."
	@echo "使用 go tool pprof 分析结果"

##@ Docker

.PHONY: docker-build
docker-build: ## 构建Docker镜像
	@echo "$(GREEN)[INFO]$(NC) 构建Docker镜像..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

.PHONY: docker-run
docker-run: ## 运行Docker容器
	@echo "$(GREEN)[INFO]$(NC) 运行Docker容器..."
	docker run -d --name $(APP_NAME) \
		-v $(PWD)/logs:/app/logs \
		$(APP_NAME):latest

.PHONY: docker-stop
docker-stop: ## 停止Docker容器
	@echo "$(GREEN)[INFO]$(NC) 停止Docker容器..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

##@ 信息

.PHONY: version
version: ## 显示版本信息
	@echo "应用名称: $(APP_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Git提交: $(GIT_COMMIT)"
	@echo "Go版本: $(GO_VERSION)"

.PHONY: info
info: ## 显示项目信息
	@echo "$(BLUE)项目信息:$(NC)"
	@echo "  名称: $(APP_NAME)"
	@echo "  版本: $(VERSION)"
	@echo "  语言: Go"
	@echo ""
	@echo "$(BLUE)目录结构:$(NC)"
	@echo "  源码: ."
	@echo "  构建: $(BUILD_DIR)"
	@echo "  发行: $(PACKAGES_DIR)"
	@echo "  发布: $(RELEASE_DIR)"
	@echo ""
	@echo "$(BLUE)支持平台:$(NC)"
	@for platform in $(PLATFORMS); do \
		echo "  - $$platform"; \
	done

# 确保脚本可执行
$(shell chmod +x build.sh release.sh quick-deploy.sh check_status.sh deploy.sh 2>/dev/null || true)

# 创建必要目录
$(shell mkdir -p $(BUILD_DIR) $(DIST_DIR) $(PACKAGES_DIR) $(RELEASE_DIR) 2>/dev/null || true)
