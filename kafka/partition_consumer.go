package kafka

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/logger"
)

func partitionsToString(partitions []int) string {
	strs := make([]string, len(partitions))
	for i, p := range partitions {
		strs[i] = strconv.Itoa(p)
	}
	return "[" + strings.Join(strs, ",") + "]"
}

// getCurrentPartitionOffset 获取指定分区的当前最新offset
func getCurrentPartitionOffset(brokers []string, topic string, partition int) (int64, error) {
	conn, err := kafka.DialLeader(context.Background(), "tcp", brokers[0], topic, partition)
	if err != nil {
		return 0, fmt.Errorf("failed to connect to partition leader: %w", err)
	}
	defer conn.Close()

	// 获取当前分区的最新offset
	lastOffset, err := conn.ReadLastOffset()
	if err != nil {
		return 0, fmt.Errorf("failed to read last offset: %w", err)
	}

	// 返回最新offset，这样就只会消费应用启动后的新消息
	return lastOffset, nil
}

// NewPartitionConsumer 创建分区专用消费者
func NewPartitionConsumer(cfg *config.Config, logManager *logger.LoggerManager, handler MessageHandler, partitions []int) (*Consumer, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 获取系统日志器用于Kafka内部日志
	systemLogger := logManager.GetLogger(logger.LogTypeSystem)

	// 获取当前时间点的最新offset，确保只消费应用启动后的消息
	currentOffset, err := getCurrentPartitionOffset(cfg.Kafka.Brokers, cfg.Kafka.Topic, partitions[0])
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to get current partition offset: %w", err)
	}

	// 创建Kafka Reader配置 - 使用手动分区分配模式避免重复消费
	// 注意：当使用手动分区分配时，不能使用GroupID，因为它们是互斥的
	readerConfig := kafka.ReaderConfig{
		Brokers:   cfg.Kafka.Brokers,
		Topic:     cfg.Kafka.Topic,
		Partition: partitions[0], // 手动指定分区（每个消费者只处理一个分区）

		// 从当前时间点开始消费，避免历史消息
		StartOffset:    currentOffset,
		MinBytes:       1e3,                    // 1KB
		MaxBytes:       10e6,                   // 10MB
		MaxWait:        100 * time.Millisecond, // 100ms
		ReadBackoffMin: 50 * time.Millisecond,
		ReadBackoffMax: 500 * time.Millisecond,

		// 连接配置
		Dialer: &kafka.Dialer{
			Timeout:   10 * time.Second,
			DualStack: true,
		},

		// 错误处理
		ErrorLogger: kafka.LoggerFunc(func(msg string, args ...interface{}) {
			systemLogger.Debugf("Kafka partition reader: "+msg, args...)
		}),
	}

	reader := kafka.NewReader(readerConfig)

	// 记录分区分配信息
	systemLogger.WithFields(logrus.Fields{
		"topic":               cfg.Kafka.Topic,
		"assigned_partitions": partitions,
		"offset_strategy":     cfg.Kafka.Consumer.OffsetInitial,
		"start_offset":        currentOffset,
		"mode":                "manual_partition_assignment",
	}).Info("Partition consumer configured with manual partition assignment")

	// 使用配置中的并行工作数
	parallelWorkers := cfg.Kafka.ParallelWorkers
	if parallelWorkers <= 0 {
		parallelWorkers = 10 // 默认10个worker
	}

	consumer := &Consumer{
		config:            cfg,
		logManager:        logManager,
		Reader:            reader,
		ctx:               ctx,
		cancel:            cancel,
		messageHandler:    handler,
		parallelWorkers:   parallelWorkers,
		processingTimeout: 5 * time.Second,
		messageChan:       make(chan *Message, parallelWorkers*2),
		workerPool:        make(chan struct{}, parallelWorkers),

		// 初始化增强字段
		pendingMessages: make(map[string]*PendingMessage),
		retryQueue:      make(chan *Message, parallelWorkers),
		commitChan:      make(chan kafka.Message, parallelWorkers*2),
		lastCommitTime:  time.Now(),
		metrics: &ConsumerMetrics{
			LastUpdateTime: time.Now(),
		},
		metricsInterval: time.Duration(cfg.App.MetricsInterval) * time.Second,
	}

	return consumer, nil
}
