package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/handler"
	"traffic-mirror/kafka"
	"traffic-mirror/logger"
)

const (
	defaultConfigPath = "config.yaml"
)

func main() {
	// 解析命令行参数
	var configPath string
	flag.StringVar(&configPath, "config", defaultConfigPath, "Path to configuration file")
	flag.Parse()

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// 设置日志管理器
	logManager, err := logger.NewLoggerManager(cfg)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to setup logger manager: %v\n", err)
		os.Exit(1)
	}

	// 记录应用启动日志
	logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
		"config_path": configPath,
		"app_name":    cfg.App.Name,
		"version":     cfg.App.Version,
	}, "Starting traffic mirror application")

	// 创建消息处理器
	messageHandler, err := handler.NewMessageHandler(cfg, logManager)
	if err != nil {
		logManager.LogSystem(logrus.FatalLevel, logrus.Fields{"error": err.Error()}, "Failed to create message handler")
		os.Exit(1)
	}

	// 创建Kafka消费者（支持多消费者模式）
	var consumer interface {
		Start() error
		Stop() error
	}

	if cfg.Kafka.MultiConsumer.Enabled {
		// 多消费者模式
		coordinator, err := kafka.NewConsumerCoordinator(cfg, logManager, messageHandler)
		if err != nil {
			logManager.LogSystem(logrus.FatalLevel, logrus.Fields{"error": err.Error()}, "Failed to create consumer coordinator")
			os.Exit(1)
		}
		consumer = coordinator

		logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
			"mode": "multi-consumer",
		}, "Using multi-consumer mode for better performance and no duplicate consumption")
	} else {
		// 单消费者模式
		singleConsumer, err := kafka.NewConsumer(cfg, logManager, messageHandler)
		if err != nil {
			logManager.LogSystem(logrus.FatalLevel, logrus.Fields{"error": err.Error()}, "Failed to create Kafka consumer")
			os.Exit(1)
		}
		consumer = singleConsumer

		logManager.LogSystem(logrus.InfoLevel, logrus.Fields{
			"mode": "single-consumer",
		}, "Using single-consumer mode")
	}

	// 启动消费者
	if err := consumer.Start(); err != nil {
		logManager.LogSystem(logrus.FatalLevel, logrus.Fields{"error": err.Error()}, "Failed to start Kafka consumer")
		os.Exit(1)
	}

	// 启动统计信息定时打印（如果启用了监控）
	var statsTicker *time.Ticker
	if cfg.App.EnableMetrics {
		statsTicker = time.NewTicker(time.Duration(cfg.App.MetricsInterval) * time.Second)
		go func() {
			for range statsTicker.C {
				messageHandler.PrintStats()
			}
		}()
	}

	logManager.LogSystem(logrus.InfoLevel, nil, "Traffic mirror application started successfully")

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 阻塞等待信号
	sig := <-sigChan
	logManager.LogSystem(logrus.InfoLevel, logrus.Fields{"signal": sig.String()}, "Received shutdown signal")

	// 优雅关闭
	logManager.LogSystem(logrus.InfoLevel, nil, "Shutting down gracefully...")

	// 停止统计信息定时器
	if statsTicker != nil {
		statsTicker.Stop()
	}

	// 停止Kafka消费者
	if err := consumer.Stop(); err != nil {
		logManager.LogError(logrus.ErrorLevel, logrus.Fields{"error": err.Error()}, "Failed to stop Kafka consumer")
	}

	// 关闭消息处理器
	if err := messageHandler.Close(); err != nil {
		logManager.LogError(logrus.ErrorLevel, logrus.Fields{"error": err.Error()}, "Failed to close message handler")
	}

	logManager.LogSystem(logrus.InfoLevel, nil, "Traffic mirror application stopped")
}
