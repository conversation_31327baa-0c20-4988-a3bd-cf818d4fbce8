#!/bin/bash

# 1分钟时间过滤功能演示脚本

echo "🚀 Traffic-Copy 1分钟时间过滤功能演示"
echo "========================================"
echo ""

echo "📋 功能概述："
echo "1. 只消费1分钟内的Kafka消息"
echo "2. 支持可配置的时间窗口和容忍度"
echo "3. 增强的日志记录，包含分区、offset和时间信息"
echo "4. 代码精简优化"
echo ""

echo "⚙️  当前配置："
echo "   - 时间窗口: 60秒（1分钟）"
echo "   - 容忍度: 30秒"
echo "   - 时间模式: kafka_time（使用Kafka消息时间戳）"
echo ""

echo "🔧 配置文件 (config.yaml)："
echo "filter:"
echo "  time_filter:"
echo "    enabled: true"
echo "    window_seconds: 60      # 1分钟时间窗口"
echo "    tolerance_seconds: 30   # 30秒容忍度"
echo "    time_mode: \"kafka_time\" # 使用Kafka时间戳"
echo ""

echo "📊 时间过滤逻辑："
echo "   - 消息在 [当前时间 - 90秒, 当前时间 + 90秒] 范围内会被处理"
echo "   - 超出此范围的消息会被跳过"
echo "   - 日志中会记录详细的时间过滤信息"
echo ""

echo "🧪 运行测试："
echo "   go test -v ./test/one_minute_filter_test.go"
echo ""

echo "📝 增强的日志示例："
echo "{"
echo "  \"topic\": \"lynxiao_flow\","
echo "  \"partition\": 0,"
echo "  \"offset\": 12345,"
echo "  \"kafka_timestamp\": \"2025-08-11 18:30:00.000\","
echo "  \"server_timestamp\": \"2025-08-11 18:30:30.000\","
echo "  \"message_id\": \"lynxiao_flow-0-12345\","
echo "  \"time_diff_ms\": 30000,"
echo "  \"processing_status\": \"forwarded\","
echo "  \"trace_id\": \"abc123\","
echo "  \"app_id\": \"HealthySearch\","
echo "  \"query\": [\"搜索内容\"]"
echo "}"
echo ""

echo "🎯 核心优势："
echo "✅ 精确时间控制 - 只处理最新的消息"
echo "✅ 详细可追踪 - 每条消息都有完整的元数据"
echo "✅ 灵活配置 - 支持多种时间模式和参数"
echo "✅ 高性能 - 优化的代码结构"
echo "✅ 生产就绪 - 完整的测试覆盖"
echo ""

echo "🔄 不同配置示例："
echo ""

echo "# 严格1分钟模式（零容忍）"
echo "filter:"
echo "  time_filter:"
echo "    enabled: true"
echo "    window_seconds: 60"
echo "    tolerance_seconds: 0     # 严格按时间窗口"
echo "    time_mode: \"kafka_time\""
echo ""

echo "# 宽松模式（2分钟窗口）"
echo "filter:"
echo "  time_filter:"
echo "    enabled: true"
echo "    window_seconds: 120      # 2分钟窗口"
echo "    tolerance_seconds: 60    # 1分钟容忍度"
echo "    time_mode: \"kafka_time\""
echo ""

echo "# 服务器时间模式"
echo "filter:"
echo "  time_filter:"
echo "    enabled: true"
echo "    window_seconds: 60"
echo "    tolerance_seconds: 30"
echo "    time_mode: \"server_time\" # 使用服务器接收时间"
echo ""

echo "🚀 启动应用："
echo "   ./traffic-mirror -config config.yaml"
echo ""

echo "📖 查看增强的日志："
echo "   tail -f logs/request.log | jq ."
echo "   # 日志现在包含：分区、offset、Kafka时间戳、时间差异等"
echo ""

echo "🧪 运行完整测试："
echo "   go test ./test/time_filter_test.go -v"
echo "   go test ./test/one_minute_filter_test.go -v"
echo ""

echo "⚙️  修改配置："
echo "   编辑 config.yaml 中的 filter.time_filter 部分"
echo ""

echo "📋 查看详细分析报告："
echo "   cat IMPLEMENTATION_SUMMARY.md"
echo ""

echo "✨ 功能已完全实现并通过测试！"
echo "   - ✅ 1分钟时间过滤"
echo "   - ✅ 增强日志记录"
echo "   - ✅ 代码精简优化"
echo "   - ✅ 完整测试覆盖"
