# 流量镜像工具 Dockerfile
# 基于现有项目结构，针对traffic-mirror项目优化

# 第一阶段：使用Go镜像来构建应用程序
FROM artifacts.iflytek.com/docker-private/datahub/go1.23.3-gcc9.4:v1.0.0 AS builder

# 设置工作目录
WORKDIR /lynxiao

# 复制go mod文件
COPY go.mod go.sum ./

# 设置Go环境变量
ENV PATH="/root/go/bin:${PATH}"
ENV GOPROXY="https://${BUILDER}:${DEPEND_APIKEY}@depend.iflytek.com/artifactory/api/go/go-repo"

# 复制源代码
COPY . .


RUN go mod tidy && go build -o ./output/traffic-mirror .

# 第二阶段：创建运行时镜像
FROM artifacts.iflytek.com/docker-private/datahub/lynxiao/ubuntu:20.04.6


# 设置工作目录
WORKDIR /lynxiao

# 从构建阶段复制二进制文件
COPY --from=builder /lynxiao/output/traffic-mirror .

# 复制配置文件
COPY --from=builder /lynxiao/config_prod.yaml ./config.yaml
