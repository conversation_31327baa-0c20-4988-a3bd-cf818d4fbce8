package forwarder

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"

	"traffic-mirror/config"
	"traffic-mirror/message"
)

// HTTPForwarder HTTP转发器
type HTTPForwarder struct {
	config     *config.Config
	logger     *logrus.Logger
	httpClient *http.Client
	metrics    *Metrics
}

// Metrics 转发统计指标
type Metrics struct {
	TotalRequests    int64 `json:"total_requests"`
	SuccessRequests  int64 `json:"success_requests"`
	FailedRequests   int64 `json:"failed_requests"`
	TotalRetries     int64 `json:"total_retries"`
	AverageLatency   int64 `json:"average_latency_ms"`
	LastRequestTime  int64 `json:"last_request_time"`
}

// NewHTTPForwarder 创建新的HTTP转发器
func NewHTTPForwarder(cfg *config.Config, logger *logrus.Logger) *HTTPForwarder {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: cfg.TargetAPI.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return &HTTPForwarder{
		config:     cfg,
		logger:     logger,
		httpClient: client,
		metrics:    &Metrics{},
	}
}

// ForwardRequest 转发请求到目标API
func (f *HTTPForwarder) ForwardRequest(request *message.SearchAPIRequest) error {
	startTime := time.Now()
	
	// 更新统计指标
	f.metrics.TotalRequests++
	f.metrics.LastRequestTime = startTime.Unix()

	// 序列化请求数据
	requestData, err := json.Marshal(request)
	if err != nil {
		f.metrics.FailedRequests++
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	// 执行转发（带重试）
	err = f.forwardWithRetry(requestData, request.Header.TraceID)
	
	// 更新延迟统计
	latency := time.Since(startTime).Milliseconds()
	f.updateAverageLatency(latency)

	if err != nil {
		f.metrics.FailedRequests++
		f.logger.WithFields(logrus.Fields{
			"trace_id": request.Header.TraceID,
			"app_id":   request.Header.AppID,
			"latency":  latency,
			"error":    err.Error(),
		}).Error("Failed to forward request")
		return err
	}

	f.metrics.SuccessRequests++
	f.logger.WithFields(logrus.Fields{
		"trace_id": request.Header.TraceID,
		"app_id":   request.Header.AppID,
		"latency":  latency,
		"url":      f.config.TargetAPI.URL,
	}).Info("Successfully forwarded request")

	return nil
}

// forwardWithRetry 带重试的转发
func (f *HTTPForwarder) forwardWithRetry(requestData []byte, traceID string) error {
	var lastErr error
	
	for attempt := 0; attempt <= f.config.TargetAPI.RetryCount; attempt++ {
		if attempt > 0 {
			f.metrics.TotalRetries++
			f.logger.WithFields(logrus.Fields{
				"trace_id": traceID,
				"attempt":  attempt,
				"max_retries": f.config.TargetAPI.RetryCount,
			}).Warn("Retrying request")
			
			// 等待重试间隔
			time.Sleep(f.config.TargetAPI.RetryInterval)
		}

		// 执行HTTP请求
		err := f.doHTTPRequest(requestData, traceID)
		if err == nil {
			return nil // 成功
		}

		lastErr = err
		f.logger.WithFields(logrus.Fields{
			"trace_id": traceID,
			"attempt":  attempt + 1,
			"error":    err.Error(),
		}).Warn("Request attempt failed")
	}

	return fmt.Errorf("all retry attempts failed, last error: %w", lastErr)
}

// doHTTPRequest 执行HTTP请求
func (f *HTTPForwarder) doHTTPRequest(requestData []byte, traceID string) error {
	// 创建HTTP请求
	req, err := http.NewRequest("POST", f.config.TargetAPI.URL, bytes.NewBuffer(requestData))
	if err != nil {
		return fmt.Errorf("failed to create http request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", fmt.Sprintf("%s/%s", f.config.App.Name, f.config.App.Version))
	req.Header.Set("X-Trace-ID", traceID)

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), f.config.TargetAPI.Timeout)
	defer cancel()
	req = req.WithContext(ctx)

	// 发送请求
	resp, err := f.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send http request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("received non-success status code: %d, body: %s", resp.StatusCode, string(respBody))
	}

	f.logger.WithFields(logrus.Fields{
		"trace_id":    traceID,
		"status_code": resp.StatusCode,
		"response_size": len(respBody),
	}).Debug("Received response from target API")

	return nil
}

// updateAverageLatency 更新平均延迟
func (f *HTTPForwarder) updateAverageLatency(latency int64) {
	if f.metrics.TotalRequests == 1 {
		f.metrics.AverageLatency = latency
	} else {
		// 使用简单的移动平均
		f.metrics.AverageLatency = (f.metrics.AverageLatency + latency) / 2
	}
}

// GetMetrics 获取转发统计指标
func (f *HTTPForwarder) GetMetrics() *Metrics {
	return &Metrics{
		TotalRequests:   f.metrics.TotalRequests,
		SuccessRequests: f.metrics.SuccessRequests,
		FailedRequests:  f.metrics.FailedRequests,
		TotalRetries:    f.metrics.TotalRetries,
		AverageLatency:  f.metrics.AverageLatency,
		LastRequestTime: f.metrics.LastRequestTime,
	}
}

// ResetMetrics 重置统计指标
func (f *HTTPForwarder) ResetMetrics() {
	f.metrics = &Metrics{}
}

// GetSuccessRate 获取成功率
func (f *HTTPForwarder) GetSuccessRate() float64 {
	if f.metrics.TotalRequests == 0 {
		return 0.0
	}
	return float64(f.metrics.SuccessRequests) / float64(f.metrics.TotalRequests) * 100
}

// Close 关闭转发器
func (f *HTTPForwarder) Close() error {
	// 关闭HTTP客户端的空闲连接
	f.httpClient.CloseIdleConnections()
	
	f.logger.WithFields(logrus.Fields{
		"total_requests":   f.metrics.TotalRequests,
		"success_requests": f.metrics.SuccessRequests,
		"failed_requests":  f.metrics.FailedRequests,
		"success_rate":     f.GetSuccessRate(),
	}).Info("HTTP forwarder closed")
	
	return nil
}
