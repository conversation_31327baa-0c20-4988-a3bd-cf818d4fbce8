version: '3.8'

services:
  # 流量镜像工具
  traffic-mirror:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERSION: "${VERSION:-1.0.0}"
        BUILD_TIME: "${BUILD_TIME}"
        GIT_COMMIT: "${GIT_COMMIT}"
    container_name: traffic-mirror
    restart: unless-stopped
    environment:
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
      - ./config_prod.yaml:/app/config.yaml:ro
    depends_on:
      - kafka
      - mock-api
    networks:
      - traffic-mirror-net
    healthcheck:
      test: ["CMD", "pgrep", "traffic-mirror"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Kafka (用于开发测试)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - traffic-mirror-net

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    networks:
      - traffic-mirror-net
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 模拟目标API
  mock-api:
    build:
      context: ./test
      dockerfile: Dockerfile.mock
    container_name: mock-api
    ports:
      - "50409:50409"
    networks:
      - traffic-mirror-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50409/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s

  # Kafka UI (可选，用于管理和监控)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - traffic-mirror-net

networks:
  traffic-mirror-net:
    driver: bridge
